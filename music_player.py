import os
import sys
import time
import speech_recognition as sr
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                           QHB<PERSON>Layout, QPushButton, QLabel, QSlider, QListWidget)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon, QColor, QPalette
import pygame
from urllib.request import urlopen, Request
from urllib.error import URLError
from urllib.parse import urlencode
import json
import base64
import websockets
import uuid
import gzip
import asyncio
from mutagen import File
from mutagen.mp3 import MP3
from datetime import datetime, timedelta

# ---- 百度语音识别设置 ----
API_KEY = 'Rhoye6N28HSzx3EVo9pI8eGI'
SECRET_KEY = '15nFGXDeC5MLO5fn7rOk8lKUCBAk8pNf'
FORMAT = 'pcm'      # 麦克风数据将转成 PCM
RATE = 16000        # 采样率 16k
CUID = 'MusicPlayer'  # 自定义设备标识
DEV_PID = 1537      # 1537=普通话(输入法模型)
ASR_URL = 'http://vop.baidu.com/server_api'
SCOPE = 'audio_voice_assistant_get'
TOKEN_URL = 'http://aip.baidubce.com/oauth/2.0/token'

# ---- 豆包语音合成设置 ----
TTS_APPID = "2212278046"
TTS_TOKEN = "ATTm_-O-wwl9T-TjdXg_YBJrmSdjq5ig"
TTS_CLUSTER = "volcano_tts"
TTS_VOICE_TYPE = "zh_male_jieshuoxiaoming_moon_bigtts"
TTS_HOST = "openspeech.bytedance.com"
TTS_API_URL = f"wss://{TTS_HOST}/api/v1/tts/ws_binary"

# TTS协议头设置
TTS_DEFAULT_HEADER = bytearray(b'\x11\x10\x11\x00')

class DemoError(Exception):
    pass

class MusicPlayer(QMainWindow):
    def __init__(self):
        super().__init__()
        # 首先初始化属性
        self.current_song_index = -1
        self.is_playing = False
        self.volume = 0.5  # 初始音量50%
        self.music_list = []
        self.current_song_length = 0  # 当前歌曲总长度（秒）
        self.play_start_time = None   # 播放开始时间
        self.pause_time = 0           # 暂停时的播放位置
        self.is_listening = False  # 添加监听状态标志
        
        # 然后初始化UI和音频
        self.initUI()
        self.initAudio()
        self.load_music_files()
        
        # 启动语音识别定时器
        self.voice_timer = QTimer()
        self.voice_timer.timeout.connect(self.listen_for_command)
        self.voice_timer.start(1000)  # 改为每1秒检查一次
        
        # 更新进度条的定时器
        self.progress_timer = QTimer()
        self.progress_timer.timeout.connect(self.update_progress)
        self.progress_timer.start(100)  # 每100ms更新一次进度
        
        # 启动时的欢迎语
        asyncio.run(self.speak_text("你好，我是你的音乐助手，请说出你的命令"))
        
    def initUI(self):
        self.setWindowTitle('语音控制音乐播放器')
        self.setGeometry(300, 300, 400, 500)
        
        # 创建中心部件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 当前播放标签
        self.current_song_label = QLabel('未播放')
        layout.addWidget(self.current_song_label)
        
        # 进度条和时间标签
        progress_layout = QHBoxLayout()
        
        # 当前时间标签
        self.time_label = QLabel('00:00')
        progress_layout.addWidget(self.time_label)
        
        # 进度条
        self.progress_slider = QSlider(Qt.Horizontal)
        self.progress_slider.setMinimum(0)
        self.progress_slider.setMaximum(1000)  # 使用1000作为最大值以获得更精确的控制
        self.progress_slider.sliderPressed.connect(self.on_progress_pressed)
        self.progress_slider.sliderReleased.connect(self.on_progress_released)
        progress_layout.addWidget(self.progress_slider)
        
        # 总时长标签
        self.duration_label = QLabel('00:00')
        progress_layout.addWidget(self.duration_label)
        
        layout.addLayout(progress_layout)
        
        # 音量控制
        volume_layout = QHBoxLayout()
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setMaximum(100)
        self.volume_slider.setValue(50)
        self.volume_slider.valueChanged.connect(self.set_volume)
        volume_layout.addWidget(QLabel('音量:'))
        volume_layout.addWidget(self.volume_slider)
        layout.addLayout(volume_layout)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        
        self.prev_button = QPushButton('上一首')
        self.play_button = QPushButton('播放')
        self.next_button = QPushButton('下一首')
        
        self.prev_button.clicked.connect(self.prev_song)
        self.play_button.clicked.connect(self.play_pause)
        self.next_button.clicked.connect(self.next_song)
        
        control_layout.addWidget(self.prev_button)
        control_layout.addWidget(self.play_button)
        control_layout.addWidget(self.next_button)
        
        layout.addLayout(control_layout)
        
        # 播放列表
        self.playlist_widget = QListWidget()
        self.playlist_widget.itemDoubleClicked.connect(self.play_selected)
        layout.addWidget(self.playlist_widget)
        
        # 语音控制状态部分改为水平布局
        voice_control_layout = QHBoxLayout()
        
        # 麦克风图标
        self.mic_icon = QLabel("🎤")  # 使用emoji作为图标
        self.mic_icon.setStyleSheet("""
            QLabel {
                font-size: 24px;
                color: gray;
                padding: 5px;
            }
        """)
        voice_control_layout.addWidget(self.mic_icon)
        
        # 语音控制状态标签
        self.voice_status = QLabel('等待命令...')
        self.voice_status.setStyleSheet("""
            QLabel {
                font-size: 14px;
                padding: 5px;
                border-radius: 10px;
                background-color: #f0f0f0;
                min-width: 150px;
            }
        """)
        voice_control_layout.addWidget(self.voice_status)
        
        # 添加一个提示标签
        self.voice_tip = QLabel('请直接说出您的命令')
        self.voice_tip.setStyleSheet("""
            QLabel {
                color: #666666;
                font-size: 12px;
            }
        """)
        voice_control_layout.addWidget(self.voice_tip)
        
        # 添加伸缩项使状态显示居中
        voice_control_layout.addStretch()
        
        layout.addLayout(voice_control_layout)
        
    def initAudio(self):
        pygame.mixer.init()
        pygame.mixer.music.set_volume(self.volume)
        
    def load_music_files(self):
        music_dir = "music"  # 音乐文件夹路径
        if os.path.exists(music_dir):
            self.music_list = [f for f in os.listdir(music_dir) 
                             if f.endswith(('.mp3', '.wav', '.ogg'))]
            self.playlist_widget.addItems(self.music_list)
            
    def play_selected(self, item):
        index = self.playlist_widget.row(item)
        self.play_song(index)
        
    def get_audio_length(self, file_path):
        """获取音频文件的长度（秒）"""
        try:
            audio = File(file_path)
            if audio is not None:
                return int(audio.info.length)
            return 0
        except Exception as e:
            print(f"获取音频长度出错: {e}")
            return 0

    def format_time(self, seconds):
        """将秒数格式化为 MM:SS 格式"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"

    def play_song(self, index):
        if 0 <= index < len(self.music_list):
            file_path = os.path.join("music", self.music_list[index])
            pygame.mixer.music.load(file_path)
            pygame.mixer.music.play()
            
            # 更新播放状态和时间
            self.current_song_index = index
            self.current_song_length = self.get_audio_length(file_path)
            self.play_start_time = datetime.now()
            self.pause_time = 0
            
            # 更新界面
            self.current_song_label.setText(f"正在播放: {self.music_list[index]}")
            self.duration_label.setText(self.format_time(self.current_song_length))
            self.is_playing = True
            self.play_button.setText('暂停')
            self.playlist_widget.setCurrentRow(index)
            
            # 确保音量设置正确
            pygame.mixer.music.set_volume(self.volume)

    def play_pause(self):
        if self.is_playing:
            pygame.mixer.music.pause()
            self.play_button.setText('播放')
            self.is_playing = False
            # 记录暂停时的播放位置
            if self.play_start_time:
                self.pause_time = (datetime.now() - self.play_start_time).total_seconds()
        else:
            if self.current_song_index == -1:
                self.play_song(0)
            else:
                # 重新加载当前歌曲并播放到暂停位置
                file_path = os.path.join("music", self.music_list[self.current_song_index])
                pygame.mixer.music.load(file_path)
                pygame.mixer.music.play(start=self.pause_time)
                # 更新播放开始时间，考虑已经播放的时间
                self.play_start_time = datetime.now() - timedelta(seconds=self.pause_time)
                self.play_button.setText('暂停')
                self.is_playing = True

    def get_current_position(self):
        """获取当前播放位置（秒）"""
        if not self.is_playing:
            return self.pause_time
        if self.play_start_time:
            return (datetime.now() - self.play_start_time).total_seconds()
        return 0

    def on_progress_pressed(self):
        """进度条被按下时暂停更新"""
        self.progress_timer.stop()

    def on_progress_released(self):
        """进度条释放时设置新的播放位置"""
        if self.current_song_length > 0:
            # 计算新的播放位置
            position = (self.progress_slider.value() / 1000.0) * self.current_song_length
            
            # 重新加载并播放到指定位置
            file_path = os.path.join("music", self.music_list[self.current_song_index])
            pygame.mixer.music.load(file_path)
            pygame.mixer.music.play(start=position)
            
            # 更新播放时间，考虑新的位置
            self.play_start_time = datetime.now() - timedelta(seconds=position)
            self.pause_time = position
            self.is_playing = True
            self.play_button.setText('暂停')
            
            # 重新启动进度更新定时器
            self.progress_timer.start()

    def update_progress(self):
        """更新进度条和时间显示"""
        if self.is_playing and self.current_song_length > 0:
            current_pos = self.get_current_position()
            
            # 更新进度条
            progress = int((current_pos / self.current_song_length) * 1000)
            self.progress_slider.setValue(progress)
            
            # 更新时间标签
            self.time_label.setText(self.format_time(current_pos))
            
            # 检查是否播放完毕
            if current_pos >= self.current_song_length:
                self.next_song()

    def next_song(self):
        next_index = (self.current_song_index + 1) % len(self.music_list)
        self.play_song(next_index)
        
    def prev_song(self):
        prev_index = (self.current_song_index - 1) % len(self.music_list)
        self.play_song(prev_index)
        
    def set_volume(self, value):
        self.volume = value / 100
        pygame.mixer.music.set_volume(self.volume)
        
    def fetch_token(self):
        params = {
            'grant_type': 'client_credentials',
            'client_id': API_KEY,
            'client_secret': SECRET_KEY
        }
        post_data = urlencode(params).encode('utf-8')
        req = Request(TOKEN_URL, post_data)
        try:
            f = urlopen(req)
            result_str = f.read().decode()
            result = json.loads(result_str)
            if ('access_token' in result.keys() and 'scope' in result.keys()):
                if SCOPE and (not SCOPE in result['scope'].split(' ')):
                    raise DemoError('scope is not correct')
                return result['access_token']
            else:
                raise DemoError('MAYBE API_KEY or SECRET_KEY not correct: access_token or scope not found in token response')
        except URLError as err:
            print('token http response http code : ' + str(err.code))
            result_str = err.read().decode()
            print(result_str)
            raise DemoError('TOKEN HTTP RESPONSE ERROR')
            
    async def speak_text(self, text):
        """将文本转换为语音并播放"""
        try:
            # 创建临时文件夹（如果不存在）
            temp_dir = "temp_audio"
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)

            # 创建临时文件
            output_file = os.path.join(temp_dir, f"response_{uuid.uuid4().hex}.mp3")

            # 构建请求JSON
            request_json = {
                "app": {
                    "appid": TTS_APPID,
                    "token": TTS_TOKEN,
                    "cluster": TTS_CLUSTER
                },
                "user": {
                    "uid": str(uuid.uuid4())
                },
                "audio": {
                    "voice_type": TTS_VOICE_TYPE,
                    "encoding": "mp3",
                    "speed_ratio": 1.0,
                    "volume_ratio": 1.0,
                    "pitch_ratio": 1.0,
                },
                "request": {
                    "reqid": str(uuid.uuid4()),
                    "text": text,
                    "text_type": "plain",
                    "operation": "submit"
                }
            }

            # 压缩请求数据
            payload_bytes = str.encode(json.dumps(request_json))
            payload_bytes = gzip.compress(payload_bytes)

            # 构建完整请求
            full_client_request = bytearray(TTS_DEFAULT_HEADER)
            full_client_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
            full_client_request.extend(payload_bytes)

            # 打开文件准备写入
            file_to_save = open(output_file, "wb")

            # 连接WebSocket并发送请求
            header = {"Authorization": f"Bearer; {TTS_TOKEN}"}
            async with websockets.connect(TTS_API_URL, extra_headers=header, ping_interval=None) as ws:
                await ws.send(full_client_request)
                while True:
                    res = await ws.recv()
                    done = self.parse_tts_response(res, file_to_save)
                    if done:
                        file_to_save.close()
                        break

            # 播放合成的语音
            if os.path.exists(output_file):
                # 暂存当前播放状态
                was_playing = self.is_playing
                current_pos = 0
                
                # 如果正在播放音乐，先暂停
                if was_playing:
                    pygame.mixer.music.pause()
                
                # 播放语音反馈
                pygame.mixer.music.load(output_file)
                pygame.mixer.music.play()
                
                # 等待语音播放完成
                while pygame.mixer.music.get_busy():
                    pygame.time.wait(100)
                
                # 如果之前在播放音乐，恢复播放
                if was_playing:
                    pygame.mixer.music.load(os.path.join("music", self.music_list[self.current_song_index]))
                    pygame.mixer.music.play()

                # 删除临时文件
                try:
                    os.remove(output_file)
                except:
                    pass

        except Exception as e:
            print(f"语音合成出错: {e}")

    def parse_tts_response(self, res, file):
        """解析TTS WebSocket响应"""
        header_size = res[0] & 0x0f
        message_type = res[1] >> 4
        message_type_specific_flags = res[1] & 0x0f
        message_compression = res[2] & 0x0f
        payload = res[header_size*4:]

        if message_type == 0xb:  # audio-only server response
            if message_type_specific_flags == 0:
                return False
            else:
                sequence_number = int.from_bytes(payload[:4], "big", signed=True)
                payload = payload[8:]
                file.write(payload)
                return sequence_number < 0
        elif message_type == 0xf:  # 错误消息
            return True
        elif message_type == 0xc:  # 处理前端消息
            return False
        else:
            return False

    def set_listening_state(self, is_listening):
        """设置监听状态的UI显示"""
        self.is_listening = is_listening
        if is_listening:
            # 正在监听状态
            self.mic_icon.setStyleSheet("""
                QLabel {
                    font-size: 24px;
                    color: #ff4444;
                    padding: 5px;
                }
            """)
            self.voice_status.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    padding: 5px;
                    border-radius: 10px;
                    background-color: #ffe6e6;
                    color: #ff4444;
                    min-width: 150px;
                }
            """)
            self.voice_tip.setText("正在听，请说出命令...")
        else:
            # 等待状态
            self.mic_icon.setStyleSheet("""
                QLabel {
                    font-size: 24px;
                    color: gray;
                    padding: 5px;
                }
            """)
            self.voice_status.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    padding: 5px;
                    border-radius: 10px;
                    background-color: #f0f0f0;
                    color: black;
                    min-width: 150px;
                }
            """)
            self.voice_tip.setText('请直接说出您的命令')

    def listen_for_command(self):
        try:
            r = sr.Recognizer()
            with sr.Microphone() as source:
                self.set_listening_state(True)  # 设置为监听状态
                # 调整麦克风的能量阈值，以更好地识别语音
                r.adjust_for_ambient_noise(source, duration=0.5)
                r.energy_threshold = 4000  # 提高能量阈值，减少误触发
                audio = r.listen(source, timeout=1, phrase_time_limit=2)
                
            try:
                # 将音频转换为PCM格式
                audio_data = audio.get_raw_data(convert_rate=RATE, convert_width=2)
                
                # 获取token
                token = self.fetch_token()
                
                # 构造请求数据
                speech = base64.b64encode(audio_data).decode('utf-8')
                params = {
                    "dev_pid": DEV_PID,
                    "format": FORMAT,
                    "rate": RATE,
                    "token": token,
                    "cuid": CUID,
                    "channel": 1,
                    "speech": speech,
                    "len": len(audio_data)
                }
                
                post_data = json.dumps(params, sort_keys=False).encode('utf-8')
                req = Request(ASR_URL, post_data)
                req.add_header('Content-Type', 'application/json')
                
                f = urlopen(req)
                result_str = f.read().decode()
                result = json.loads(result_str)
                
                if 'result' in result:
                    text = result['result'][0].lower()
                    self.voice_status.setText(f"识别到: {text}")
                    self.process_command(text)
                    
            except sr.UnknownValueError:
                self.voice_status.setText("等待命令...")
            except sr.RequestError as e:
                self.voice_status.setText(f"语音识别服务错误: {e}")
            except Exception as e:
                self.voice_status.setText(f"错误: {str(e)}")
                
        except Exception as e:
            self.voice_status.setText("等待命令...")
        finally:
            self.set_listening_state(False)  # 恢复等待状态

    def process_command(self, command):
        """处理语音命令并提供语音反馈"""
        response = None
        
        # 播放控制命令
        if any(word in command for word in ['播放', '开始', '继续']):
            if not self.is_playing:
                response = "好的，开始播放音乐"
            else:
                response = "音乐已经在播放了"
            self.play_pause()
            
        elif any(word in command for word in ['暂停', '停止']):
            if self.is_playing:
                response = "好的，暂停播放"
            else:
                response = "音乐已经暂停了"
            self.play_pause()
            
        elif any(word in command for word in ['下一首', '切歌']):
            response = "正在切换到下一首"
            self.next_song()
            
        elif any(word in command for word in ['上一首', '返回']):
            response = "正在切换到上一首"
            self.prev_song()
            
        # 音量控制命令 - 增加更多命令词
        elif any(word in command for word in [
            '增大音量', '声音大点', '调大声', '大声点', '音量大点',
            '声音调大', '调高音量', '音量调大', '放大声音', '音量调高',
            '声音放大', '增加音量', '加大音量', '提高音量', '音量增大',
            '大声一点', '大一点声', '声音再大点', '再大点声音'
        ]):
            current = self.volume_slider.value()
            if current < 100:
                self.volume_slider.setValue(min(current + 10, 100))
                response = "已经把音量调大了"
            else:
                response = "音量已经是最大了"
                
        elif any(word in command for word in [
            '减小音量', '声音小点', '调小声', '小声点', '音量小点',
            '声音调小', '调低音量', '音量调小', '降低声音', '音量调低',
            '声音变小', '减少音量', '降低音量', '音量减小', '声音降低',
            '小声一点', '小一点声', '声音再小点', '再小点声音'
        ]):
            current = self.volume_slider.value()
            if current > 0:
                self.volume_slider.setValue(max(current - 10, 0))
                response = "已经把音量调小了"
            else:
                response = "音量已经是最小了"

        # 音量最大/最小命令
        elif any(word in command for word in [
            '最大音量', '声音最大', '音量最大', '调到最大', '声音调到最大',
            '音量调到最大', '声音开到最大', '最大声', '调最大声'
        ]):
            if self.volume_slider.value() < 100:
                self.volume_slider.setValue(100)
                response = "已经把音量调到最大了"
            else:
                response = "音量已经是最大了"

        elif any(word in command for word in [
            '最小音量', '声音最小', '音量最小', '调到最小', '声音调到最小',
            '音量调到最小', '声音开到最小', '最小声', '调最小声', '静音'
        ]):
            if self.volume_slider.value() > 0:
                self.volume_slider.setValue(0)
                response = "已经把音量调到最小了"
            else:
                response = "音量已经是最小了"
        
        # 如果没有识别到任何命令
        else:
            response = "抱歉，我没有听清，请再说一遍"

        # 提供语音反馈
        if response:
            asyncio.run(self.speak_text(response))

def main():
    app = QApplication(sys.argv)
    player = MusicPlayer()
    player.show()
    sys.exit(app.exec_())

if __name__ == '__main__':
    main() 